<template>
  <div class="content" ref="fullscreenContent">


    <div class="unit" v-if="isFullscreen">流量单位：m³/s</div>
    <div class="fullscreen" @click="toggleFullScreen" :style="getBackgroundStyle()"></div>
    <div class="target" style="height: 100%; width: 100%;" v-if="isFullscreen">
      <!-- 左第一列 -->
      <div class="item-number" :style="{ top: 'calc(4.11rem - 0.16rem)', left: '3.35rem' }">{{ getData('合济七闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(4.7rem - 0.16rem)', left: '3.35rem' }">{{ getData('合济六闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(5.32rem - 0.16rem)', left: '3.35rem' }">{{ getData('合济五闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(5.93rem - 0.16rem)', left: '3.35rem' }">{{ getData('合济四闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(6.54rem - 0.16rem)', left: '3.35rem' }">{{ getData('合济三闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(7.19rem - 0.16rem)', left: '3.35rem' }">{{ getData('合济二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(7.9rem - 0.16rem)', left: '3.35rem' }">{{ getData('合济一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(8.44rem - 0.16rem)', left: '3.35rem' }">{{ getData('合济进水') }}</div>
      <!-- 左第二列 -->
      <div class="item-number" :style="{ top: '4.93rem', left: '5.77rem' }">{{ getData('永兰五闸') }}</div>
      <div class="item-number" :style="{ top: '5.55rem', left: '5.77rem' }">{{ getData('永兰四闸') }}</div>
      <div class="item-number" :style="{ top: '6.2rem', left: '5.77rem' }">{{ getData('永兰三闸') }}</div>
      <div class="item-number" :style="{ top: '7.15rem', left: '5.77rem' }">{{ getData('永兰二闸') }}</div>
      <!-- 左第三列 -->
      <div class="item-number" :style="{ top: 'calc(1.15rem - 0.16rem)', left: '6.85rem' }">{{ getData('西乐泄水') }}</div>
      <div class="item-number" :style="{ top: 'calc(2.14rem - 0.16rem)', left: '6.45rem' }">{{ getData('西乐四闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(2.94rem - 0.16rem)', left: '6.45rem' }">{{ getData('西乐三闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(3.73rem - 0.16rem)', left: '6.45rem' }">{{ getData('西乐二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(4.59rem - 0.16rem)', left: '6.45rem' }">{{ getData('西乐一闸') }}</div>
      <!-- 左第四列 -->
      <div class="item-number" :style="{ top: 'calc(2.92rem)', left: '7.78rem' }">{{ getData('西乐南渠进水闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(4.58rem)', left: '7.78rem' }">{{ getData('西乐西渠进水闸') }}</div>
      <!-- 左第五列 -->
      <div class="item-number" :style="{ top: 'calc(1.47rem - 0.16rem)', left: '8.69rem' }">{{ getData('西乐南渠尾口') }}</div>
      <div class="item-number" :style="{ top: 'calc(2.1rem - 0.16rem)', left: '8.69rem' }">{{ getData('西乐南渠三闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(2.51rem - 0.16rem)', left: '8.69rem' }">{{ getData('西乐南渠二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(2.91rem - 0.16rem)', left: '8.69rem' }">{{ getData('西乐南渠一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(3.35rem - 0.16rem)', left: '8.69rem' }">{{ getData('西乐西渠三闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(4.55rem - 0.16rem)', left: '8.69rem' }">{{ getData('西乐西渠二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(4.01rem - 0.16rem)', left: '8.69rem' }">{{ getData('西乐西渠一闸') }}</div>

      <!-- 左第六列 -->
      <div class="item-number" :style="{ top: 'calc(1.08rem - 0.16rem)', left: '9.8rem' }">{{ getData('正稍八闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(1.73rem - 0.16rem)', left: '9.8rem' }">{{ getData('正稍七闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(2.44rem - 0.16rem)', left: '9.8rem' }">{{ getData('正稍六闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(3.07rem - 0.16rem)', left: '9.8rem' }">{{ getData('正稍五闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(3.69rem - 0.16rem)', left: '9.8rem' }">{{ getData('正稍四闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(4.37rem - 0.16rem)', left: '9.8rem' }">{{ getData('正稍三闸') }}</div>

      <!-- 斜方向 -->
      <div class="item-number" :style="{ top: 'calc(4.6rem)', left: '10.64rem' }">{{ getData('大退水进水闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(3rem)', left: '13.25rem' }">{{ getData('大退水一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(2.28rem)', left: '13.93rem' }">{{ getData('大退水二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(1.62rem)', left: '14.6rem' }">{{ getData('大退水三闸') }}</div>

      <!-- 第一行 -->
      <div class="item-number" :style="{ top: 'calc(5.54rem - 0.16rem)', left: '9.94rem' }">{{ getData('西乐进水') }}</div>
      <div class="item-number" :style="{ top: 'calc(5.54rem - 0.16rem)', left: '10.58rem' }">{{ getData('新华进水') }}</div>
      <!-- 第二行 -->
      <div class="item-number" :style="{ top: 'calc(6.1rem)', left: '6.82rem' }">{{ getData('西乐光明闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(6.1rem)', left: '10.24rem' }">{{ getData('干渠二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(6.1rem)', left: '13.73rem' }">{{ getData('新华一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(6.1rem)', left: '15.79rem' }">{{ getData('新华二闸') }}</div>

      <!-- 第三行 -->
      <div class="item-number" :style="{ top: 'calc(6.96rem)', left: '9.88rem' }">{{ getData('永乐进水') }}</div>
      <div class="item-number" :style="{ top: 'calc(6.96rem)', left: '10.56rem' }">{{ getData('永刚进水') }}</div>
      <div class="item-number" :style="{ top: 'calc(7.04rem)', left: '14.43rem' }">{{ getData('永刚二闸') }}</div>

      <!-- 第4行 -->
      <div class="item-number" :style="{ top: 'calc(7.73rem)', left: '7rem' }">{{ getData('永兰一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(7.71rem)', left: '10.3rem' }">{{ getData('干渠一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(7.71rem)', left: '12.35rem' }">{{ getData('永刚一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(7.71rem)', left: '16.7rem' }">{{ getData('永刚三闸') }}</div>

      <!-- 第5行 -->
      <div class="item-number" :style="{ top: 'calc(8.1rem)', left: '10.51rem' }">{{ getData('北边一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(8.1rem)', left: '11.85rem' }">{{ getData('北边二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(8.1rem)', left: '13.14rem' }">{{ getData('北边三闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(8.1rem)', left: '14.42rem' }">{{ getData('北边四闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(8.1rem)', left: '15.73rem' }">{{ getData('北边五闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(8.1rem)', left: '17rem' }">{{ getData('北边六闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(8.27rem)', left: '18.37rem' }">{{ getData('北边退水闸') }}</div>

      <!-- 第7行 -->
      <div class="item-number" :style="{ top: 'calc(9.87rem)', left: '6.66rem' }">{{ getData('南边一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(9.87rem)', left: '7.76rem' }">{{ getData('南边二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(9.87rem)', left: '8.9rem' }">{{ getData('南边三闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(9.87rem)', left: '10.02rem' }">{{ getData('南边四闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(9.87rem)', left: '11.12rem' }">{{ getData('南边五闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(9.87rem)', left: '12.24rem' }">{{ getData('南边六闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(9.87rem)', left: '13.32rem' }">{{ getData('南边七闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(9.87rem)', left: '14.53rem' }">{{ getData('南边八闸') }}</div>


      <div class="item-number" :style="{ top: 'calc(3.94rem - 0.16rem)', left: '14.1rem' }">{{ getData('新华西稍二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(4.78rem - 0.16rem)', left: '14.1rem' }">{{ getData('新华西稍一闸') }}</div>

      <div class="item-number" :style="{ top: 'calc(3.68rem)', left: '16.25rem' }">{{ getData('新华四闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(4.65rem)', left: '16.1rem' }">{{ getData('新华三闸') }}</div>


      <div class="item-number" :style="{ top: 'calc(6.23rem)', left: '14.93rem' }">{{ getData('西济二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(6.82rem)', left: '14.93rem' }">{{ getData('西济一闸') }}</div>

      <div class="item-number" :style="{ top: 'calc(5.83rem)', left: '16.72rem' }">{{ getData('永刚五闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(7rem)', left: '16.3rem' }">{{ getData('永刚四闸') }}</div>

      <div class="item-number" :style="{ top: 'calc(8.84rem)', left: '6.98rem' }">{{ getData('总干二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(8.35rem)', left: '7.87rem' }">{{ getData('干渠进水') }}</div>
      <div class="item-number" :style="{ top: 'calc(8.44rem)', left: '8.79rem' }">{{ getData('北边进水') }}</div>

    </div>


    <div style="width: 100%; height: 30px;position: absolute;bottom: 15px; padding: 10px;" v-if="isFullscreen">
      <TimePlaySlider v-if="times.length" :times="times" @onTimeChange="onTimeChange" />
    </div>
  </div>
</template>

<script lang="jsx">
import TimePlaySlider from '@/components/TimePlaySlider/index.vue'
import screenfull from 'screenfull'
import { getOptions } from '@/api/common'
import { mapboxPopup } from './popup.js'
import initLayer from './initLayer.js'

export default {
  name: 'GeneralizedMap',
  props: ['mapData'],
  components: { TimePlaySlider },
  data() {
    return {
      mapIns: null,
      times: [],
      isFullscreen: false,
      currentTime: '',
    }
  },
  computed: {},
  watch: {
    mapData: {
      handler(newVal, oldVal) {},
      deep: true,
    },
  },
  mounted() {
    let firstkey = Object.keys(this.mapData).length ? Object.keys(this.mapData)[0] : ''
    if(firstkey){
      this.times = [...new Set(Object.keys(this.mapData[firstkey]?.timeData))] 
      this.currentTime = this.times[0] || ''
    }
  },
  methods: {
    getBackgroundStyle() {
      return {
        'background-image': this.isFullscreen ? `url(${require('@/assets/images/closeFullscreen.png')})` : `url(${require('@/assets/images/fullscreen.png')})`  ,
        'background-size': '100% 100%',
        'background-repeat': 'no-repeat',
      };
    },
    getData(name) {
      if (this.mapData[name]) {
        return this.mapData[name].timeData[this.currentTime].q
      } else {
        return '暂无'
      }
    },
    toggleFullScreen() {
      if (screenfull.isEnabled) {
        this.isFullscreen = !this.isFullscreen
        screenfull.toggle(this.$refs.fullscreenContent)
      } else {
        this.$message({
          message: '您的浏览器不支持全屏功能',
          type: 'warning'
        })
      }
    },
    onTimeChange(time) {
      this.currentTime = time
    }
  },
}
</script>

<style lang="less" scoped>
.content {
  height: 100%;
  width: 100%;
  margin: 5px 10px;
  // background: #f5f5f5;
  background: url('~@/assets/images/GeneralizedMap.png') no-repeat;
  background-size: 100% 100%;
  // display: flex;
  position: relative;
  .fullscreen {
    position: absolute;
    top: 0.1rem;
    right: 0.1rem;
    height: 0.4rem;
    width: 0.4rem;
  }

  .unit {
    position: absolute;
    top: 0.1rem;
    left: 0.1rem;
    display: flex;
    font-size: 0.12rem;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0em;
    color: #1d2129;
    align-items: center;
  }

  .item-number {
    position: absolute;
    height: 0.14rem;
    border-radius: 2px;
    opacity: 1;
    font-family: PingFang SC;
    font-size: 0.1rem;
    font-weight: 500;
    line-height: 0.14rem;
    display: flex;
    align-items: center;
    letter-spacing: -0.09px;
    z-index: 0;
    background: #FFF7E8;
    box-sizing: border-box;
    border: 1px solid #FFE4BA;
    display: flex;
    flex-direction: column;
    padding: 0px 2px;
    gap: 10px;
    color: #FF7D00;
  }

}
</style>
