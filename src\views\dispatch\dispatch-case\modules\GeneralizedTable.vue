<template>
  <div style="height: 100%; margin-left: 0px; margin-top: 0px; background-color: red">
    <VxeTable ref="vxeTableRef" size="small" :isShowTableHeader="false" :isDrop="false" :columns="columns"
      :tableData="allData" :loading="false" :tablePage="false"></VxeTable>
    <div class="curve-panel" v-if="!!selectedShuiZha">
      <div class="header">
        <div class="name">{{ selectedShuiZha?.projectName }}</div>
        <div class="close" @click="clearChart">X</div>
      </div>
      <div class="chart">
        <LineEchart :height="'220px'" :width="'100%'" :dataSource="lineChartData" :custom="lineChartCustom">
        </LineEchart>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
import VxeTable from '@/components/VxeTable/index.vue'
import LineEchart from './Linechart/index.vue'
export default {
  name: 'ResultTable',
  props: ['dataSource', 'mapData'],
  components: { VxeTable, LineEchart },
  data() {
    return {
      active: undefined,
      allData: [],
      selectedShuiZha: null,
      columns: [
        { type: 'seq', title: '序号', width: 50 },
        {
          title: '调度对象',
          fixed: 'left',
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              return (
                <span>
                  {
                    <span>
                      <a onClick={() => this.projectClick(row)}>{row.projectName}</a>
                    </span>
                  }
                </span>
              )
            },
          },
        },
        {
          field: 'parentProjectName',
          title: '所属干渠',
          minWidth: 80,
        },
        {
          field: 'startUpWlv',
          title: '初始上游水位(m)',
          minWidth: 120,
        },
        {
          field: 'startDownWlv',
          title: '初始下游水位(m)',
          minWidth: 120,
        },
        {
          field: 'endUpWlv',
          title: '末期上游水位(m)',
          minWidth: 120,
        },
        {
          field: 'endDownWlv',
          title: '末期下游水位(m)',
          minWidth: 120,
        },
        {
          field: 'totalFlow',
          title: '累计过闸流量(万m³)',
          minWidth: 150,
        }
      ],
      lineChartCustom: {
        shortValue: true, // 缩写坐标值
        xLabel: '', // x轴名称
        yLabel: '水位(m)', //y轴名称
        yUnit: '', //y轴单位
        legend: true, // 图例
        showAreaStyle: true, // 颜色区域
        rYUnit: '', // 右侧y轴单位
        rYLabel: '流量(m³/s)', // 右侧y轴名称
        rYInverse: false, // 右侧y轴是否反向
        yNameLocation: "end",
        dataZoom: false,
        color: null,
        grid: {
          left: '1%',
          right: '1%',
          bottom: '5%',
          top: '15%',
          containLabel: true,
        },
        legendOptions: {
          orient: 'horizontal',
        },
        legendTop: '1%',
        legendLeft: '40%',
        xAxisData: []
      },
      lineChartData: []
    }
  },
  computed: {},
  watch: {
    dataSource: {
      handler(newVal, oldVal) {
        // this.active = newVal?.[0]?.projectId
        this.allData = newVal
      },
      immediate: true,
    },
  },
  created() { },
  methods: {
    projectClick(item) {
      this.selectedShuiZha = item
      this.refreshBottomChart(item)
    },
    clearChart() {
      this.selectedShuiZha = null
      this.lineChartData = []
    },
    refreshBottomChart(newVal) {
      let tagetObj
      Object.keys(this.mapData).forEach(key => {
        if (this.mapData[key].projectId == newVal.projectId) {
          tagetObj = this.mapData[key].timeData
        }
      });
      if(!tagetObj){
        return
      }
      let times = Object.keys(tagetObj)
      let downWlv = []
      let q = []
      let upWlv = []
      times.forEach((element, index) => {
        downWlv.push(tagetObj[element].downWlv)
        q.push(+(tagetObj[element].q))
        upWlv.push(+(tagetObj[element].upWlv))
      });

      let res = [{
        name: '上游水位',
        color: '#507EF7',
        yAxisIndex: 0,
        data: upWlv
      },
      {
        name: '过闸流量',
        color: '#F7BA1E',  
        yAxisIndex: 1,
        data: q
      },
      {
        name: '下游水位',
        color: '#B5E241',
        yAxisIndex: 1,
        data: downWlv
      }]
      this.lineChartCustom.xAxisData = times
      this.lineChartData = res
    },
  },
}
</script>

<style lang="less" scoped>
:deep(.ant-table-thead > tr > th) {
  background-color: #f2f3f5;
  padding: 10px 4px;
  font-size: 13px;
  font-weight: 500;
  height: 100px;
}

.curve-panel {
  position: absolute;
  z-index: 99999999;
  bottom: 5%;
  right: 8px;
  width: 580px;
  display: flex;
  border-radius: 4px;
  overflow: hidden;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  border: 2px solid rgba(78, 89, 105, 0.3);

  .header {
    background: #f2f3f5;
    color: #1d2129;
    line-height: 20px;
    padding: 6px 8px;
    display: flex;
    // align-items: center;

    .icon {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #0d9c47;
      color: #fff;
      display: inline-block;
      text-align: center;
      line-height: 20px;
    }

    .name {
      flex: 1;
      font-weight: 600;
      margin: 0 0 0 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .close {
      margin-left: auto;
      cursor: pointer;
      width: 20px;
      height: 20px;
      text-align: center;
      line-height: 20px;
      font-size: 12px;
      color: #8f959e;
    }
  }

  .chart {
    flex: 1;
    padding-top: 10px;
  }
}
</style>
