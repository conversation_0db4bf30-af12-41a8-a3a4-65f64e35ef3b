import { defineConfig, loadEnv } from '@rsbuild/core'
import { pluginBabel } from '@rsbuild/plugin-babel'
import { pluginVue2 } from '@rsbuild/plugin-vue2'
import { pluginVue2Jsx } from '@rsbuild/plugin-vue2-jsx'
import { pluginLess } from '@rsbuild/plugin-less'
import { pluginNodePolyfill } from '@rsbuild/plugin-node-polyfill'
import vueLegacy from 'rsbuild-plugin-vue-legacy'
import path, { resolve } from 'path'
import { pluginSvgSpriteLoader } from 'rsbuild-svg-sprite-loader'

const { publicVars } = loadEnv({ prefixes: ['VUE_APP_'] })

export default defineConfig({
  plugins: [
    pluginBabel({
      include: /\.(?:jsx|tsx)$/,
      exclude: /[\\/]node_modules[\\/]/,
    }),
    pluginVue2(),
    pluginVue2Jsx({
      vueJsxOptions: {
        compositionAPI: true,
        functional: true,
        injectH: true,
        vModel: true,
        vOn: true,
      },
    }),
    vueLegacy(),
    pluginNodePolyfill(),

    pluginSvgSpriteLoader({
      path: path.join(__dirname, 'src/assets/icons/svg'),
      symbolId: 'icon-[name]',
    }),

    pluginLess({
      lessLoaderOptions: {
        lessOptions: {
          modifyVars: {
            'primary-color': '#5384FE',
            'error-color': '#ED4D37',
            'btn-danger-bg': '#ED4D37',
            'btn-danger-border': '#ED4D37',
            'success-color': '#48BB78',
            'text-color': '#4A5568',
            'text-color-secondary': '#A0AEC0',
            'btn-default-color': '#718096',
            'border-color-base': '#CBD5E0',
            'input-placeholder-color': '#A0AEC0',
            'tooltip-bg': 'rgba(26,32,44,.8)',
            'heading-color': '#4A5568',
            'background-color-base': '#F7F8FA',
          },
          javascriptEnabled: true,
          math: 'always',
        },
      },
    }),
  ],
  source: {
    entry: {
      index: './src/main.js',
    },
    define: {
      // ...publicVars,
      'process.env': {
        VUE_APP_ENV: `'${process.env.VUE_APP_ENV}'`,
        VUE_APP_NAME: `'${process.env.npm_config_name || process.env.VUE_APP_NAME}'`,
        VUE_APP_APPID: `'${process.env.npm_config_appid || process.env.VUE_APP_APPID}'`,
        VUE_APP_FAVICON: `'${process.env.npm_config_favicon || process.env.VUE_APP_FAVICON}'`,

        VUE_APP_BASE_API: `'${process.env.npm_config_base_api || process.env.VUE_APP_BASE_API}'`,
        VUE_APP_CAS_URL: `'${process.env.npm_config_cas_url || process.env.VUE_APP_CAS_URL}'`,

        // geoserver
        VUE_APP_GEOSERVER_BASE: `'${process.env.npm_config_geoserver_base || process.env.VUE_APP_GEOSERVER_BASE}'`,

        // 天地图
        VUE_APP_TIANDI_BASE: `'${process.env.npm_config_tiandi_base || process.env.VUE_APP_TIANDI_BASE}'`,
        VUE_APP_TIANDI_TK: `'${process.env.npm_config_tiandi_tk || process.env.VUE_APP_TIANDI_TK}'`,

        // MinIO
        VUE_APP_MINIO_URL: `'${process.env.npm_config_minio_url || process.env.VUE_APP_MINIO_URL}'`,
        VUE_APP_MINIO_BUCKET: `'${process.env.npm_config_minio_bucket || process.env.VUE_APP_MINIO_BUCKET}'`,
        VUE_APP_MINIO_ACCESSKEY: `'${process.env.npm_config_minio_assesskey || process.env.VUE_APP_MINIO_ACCESSKEY}'`,
        VUE_APP_MINIO_SECRETKEY: `'${process.env.npm_config_minio_secretkey || process.env.VUE_APP_MINIO_SECRETKEY}'`,
      },
    },
  },
  html: {
    template: './public/index.html',
    mountId: 'app',
    inject: 'body',
  },
  tools: {
    rspack: {
      resolve: {
        extensions: ['.vue', '.js', '.jsx', '.tsx', '.ts', '.json'],
        alias: {
          '@': path.resolve(__dirname, 'src'),
          '@assets': path.resolve(__dirname, 'src/assets'),
        },
      },
    },
  },
  performance: {
    chunkSplit: {
      strategy: 'split-by-experience',
    },
    removeConsole: false,
    removeMomentLocale: true,
  },
  output: {
    polyfill: 'usage',
    filenameHash: 'contenthash:10',
  },
  server: {
    proxy: {
      '/proxy-api': {
        target: process.env.VUE_APP_BASE_API,
        secure: false, // 设置支持https协议的代理
        changeOrigin: true,
        pathRewrite: { '^/proxy-api': '' },
        onProxyRes(proxyRes) {
          proxyRes.headers['x-real-url'] = process.env.VUE_APP_BASE_API || ''
        },
      },
      '/api': {
        target: process.env.VUE_AI_API,
        changeOrigin: true,
      },
      "/downloads": {
        target: process.env.VUE_APP_GEOSERVER_BASE,
        changeOrigin: true,
        pathRewrite: { '^/downloads': '' },
      },
    },
  },
})
