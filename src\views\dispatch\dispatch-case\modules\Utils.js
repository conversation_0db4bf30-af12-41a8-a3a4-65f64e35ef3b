// 色带
export const colors = [
    "#0000ff", "#0004ff", "#0008ff", "#000cff", "#0010ff", "#0014ff", "#0018ff", "#001cff", "#0020ff", "#0024ff",
    "#0028ff", "#002cff", "#0030ff", "#0034ff", "#0038ff", "#003cff", "#0040ff", "#0044ff", "#0048ff", "#004cff",
    "#0050ff", "#0054ff", "#0058ff", "#005cff", "#0060ff", "#0064ff", "#0068ff", "#006cff", "#0070ff", "#0074ff",
    "#0078ff", "#007cff", "#0080ff", "#0084ff", "#0088ff", "#008cff", "#0090ff", "#0094ff", "#0098ff", "#009cff",
    "#00a0ff", "#00a4ff", "#00a8ff", "#00acff", "#00b0ff", "#00b4ff", "#00b8ff", "#00bcff", "#00c0ff", "#00c4ff",
    "#00c8ff", "#00ccff", "#00d0ff", "#00d4ff", "#00d8ff", "#00dcff", "#00e0ff", "#00e4ff", "#00e8ff", "#00ecff",
    "#00f0ff", "#00f4ff", "#00f8ff", "#00fcff", "#00ffff", "#04fffc", "#08fff8", "#0cfff4", "#10fff0", "#14ffec",
    "#18ffe8", "#1cffe4", "#20ffe0", "#24ffdc", "#28ffd8", "#2cffd4", "#30ffd0", "#34ffcc", "#38ffc8", "#3cffc4",
    "#40ffc0", "#44ffbc", "#48ffb8", "#4cffb4", "#50ffb0", "#54ffac", "#58ffa8", "#5cffa4", "#60ffa0", "#64ff9c",
    "#68ff98", "#6cff94", "#70ff90", "#74ff8c", "#78ff88", "#7cff84", "#80ff80", "#84ff7c", "#88ff78", "#8cff74",
    "#90ff70", "#94ff6c", "#98ff68", "#9cff64", "#a0ff60", "#a4ff5c", "#a8ff58", "#acff54", "#b0ff50", "#b4ff4c",
    "#b8ff48", "#bcff44", "#c0ff40", "#c4ff3c", "#c8ff38", "#ccff34", "#d0ff30", "#d4ff2c", "#d8ff28", "#dcff24",
    "#e0ff20", "#e4ff1c", "#e8ff18", "#ecff14", "#f0ff10", "#f4ff0c", "#f8ff08", "#fcff04", "#ffff00", "#fffc00",
    "#fff800", "#fff400", "#fff000", "#ffec00", "#ffe800", "#ffe400", "#ffe000", "#ffdc00", "#ffd800", "#ffd400",
    "#ffd000", "#ffcc00", "#ffc800", "#ffc400", "#ffc000", "#ffbc00", "#ffb800", "#ffb400", "#ffb000", "#ffac00",
    "#ffa800", "#ffa400", "#ffa000", "#ff9c00", "#ff9800", "#ff9400", "#ff9000", "#ff8c00", "#ff8800", "#ff8400",
    "#ff8000", "#ff7c00", "#ff7800", "#ff7400", "#ff7000", "#ff6c00", "#ff6800", "#ff6400", "#ff6000", "#ff5c00",
    "#ff5800", "#ff5400", "#ff5000", "#ff4c00", "#ff4800", "#ff4400", "#ff4000", "#ff3c00", "#ff3800", "#ff3400",
    "#ff3000", "#ff2c00", "#ff2800", "#ff2400", "#ff2000", "#ff1c00", "#ff1800", "#ff1400", "#ff1000", "#ff0c00",
    "#ff0800", "#ff0400", "#ff0000"
]

export const waterColors = [
    // 红色系
    '#FF0000', '#FF1A00', '#FF3300', '#FF4D00', '#FF6600', '#FF8000', '#FF9900', '#FFB300', '#FFCC00', '#FFE600',
    // 橙色系
    '#FFE600', '#FFF200', '#FFFF00', '#F2FF00', '#E6FF00', '#D9FF00', '#CCFF00', '#BFFF00', '#B3FF00', '#A6FF00',
    // 黄色系
    '#A6FF00', '#99FF00', '#8CFF00', '#80FF00', '#73FF00', '#66FF00', '#59FF00', '#4DFF00', '#40FF00', '#33FF00',
    // 绿色系
    '#33FF00', '#26FF00', '#1AFF00', '#0DFF00', '#00FF00', '#00FF0D', '#00FF1A', '#00FF26', '#00FF33', '#00FF40',
    // 青色系
    '#00FF40', '#00FF4D', '#00FF59', '#00FF66', '#00FF73', '#00FF80', '#00FF8C', '#00FF99', '#00FFA6', '#00FFB3',
    // 蓝色系
    '#00FFB3', '#00FFCC', '#00FFE6', '#00FFFF', '#00E6FF', '#00CCFF', '#00B3FF', '#0099FF', '#0080FF', '#0066FF',
    // 紫色系
    '#0066FF', '#004DFF', '#0033FF', '#001AFF', '#0000FF', '#1A00FF', '#3300FF', '#4D00FF', '#6600FF', '#8000FF'
].reverse();

const POINTS = {
    "type": "FeatureCollection",
    "features": [],
    "crs": {
        "type": "name",
        "properties": {
            "name": "urn:ogc:def:crs:EPSG::4326"
        }
    }
}



let shuiZha = {
    "谈家埭排涝闸": {
        "coordinates": [120.49094384, 30.40169678],
        "river": "中出盐港",
        "code": 20016,
    },
    "姚家涧排涝闸": {
        "coordinates": [120.45507825, 30.44809209],
        "river": "姚家涧河（干沟4）",
        "code": 20012,
    },
    "许村翻水站": {
        "coordinates": [120.35688819, 30.43358817],
        "river": "上塘河",
        "code": 10005,
    },
    "长安翻水站": {
        "coordinates": [120.44740595, 30.45867351],
        "river": "上闸涧",
        "code": 20029,
    },
    "长安排涝闸": {
        "coordinates": [120.45001398, 30.45138164],
        "river": "上塘河",
        "code": 10005,
    },
    "盐官翻水站": {
        "coordinates": [120.5493682, 30.40478259],
        "river": "新塘河东段（总干渠2）",
        "code": 10004,
    },
    "花山汇排涝闸": {
        "coordinates": [120.78075539, 30.37450386],
        "river": "新塘河东段（总干渠2）",
        "code": 10004,
    },
    "新仓闸站": {
        "coordinates": [120.69070899, 30.40802858],
        "river": "新仓港（干渠12）",
        "code": 10051,
    },
    "盐仓排涝东闸": {
        "coordinates": [120.41827307, 30.37042383],
        "river": "堤河",
        "code": 20030,
    },
    "盐仓排涝西闸": {
        "coordinates": [120.40367087, 30.33657967],
        "river": "堤河",
        "code": 20030,
    }
}



function getColor(value, minValue, maxValue) {
    // 将值映射到 0 到 1 之间
    const normalizedValue = (value - minValue) / (maxValue - minValue); // 计算归一化值
    // 根据归一化值计算颜色索引
    const index = Math.floor(normalizedValue * (waterColors.length - 1)); // 计算颜色索引
    // 返回对应的颜色
    return waterColors[index];
}

function getSize(value, minValue, maxValue) {
    // 将值映射到 0 到 1 之间
    const normalizedValue = (value - minValue) / (maxValue - minValue); // 计算归一化值
    // 返回对应的颜色
    return normalizedValue * 0.12 + 0.03;
}

function getSizeAbs(value, minValue, maxValue) {
    let maxAbs = Math.max(Math.abs(minValue), Math.abs(maxValue));
    // 将值映射到 0 到 1 之间
    const normalizedValue = Math.abs(value) / maxAbs; // 计算归一化值
    // 返回对应的颜色
    return normalizedValue * 0.12 + 0.03;
}

function calculateBearing(latlon1, latlon2) {
    // 将经纬度从度转换为弧度
    const lat1Rad = latlon1[1] * Math.PI / 180;
    const lon1Rad = latlon1[0] * Math.PI / 180;
    const lat2Rad = latlon2[1] * Math.PI / 180;
    const lon2Rad = latlon2[0] * Math.PI / 180;
    // 计算经度差值
    const deltaLon = lon2Rad - lon1Rad;

    // 使用atan2函数计算方位角
    const y = Math.sin(deltaLon) * Math.cos(lat2Rad);
    const x = Math.cos(lat1Rad) * Math.sin(lat2Rad) - Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(deltaLon);
    let bearingRad = Math.atan2(y, x);

    // 将方位角从弧度转换为度
    let bearingDeg = bearingRad * 180 / Math.PI;

    // 将方位角归一化到0到360度之间
    if (bearingDeg < 0) {
        bearingDeg += 360;
    }

    return bearingDeg;
}

// 提取 水文监测站 时间轴 最大最小水位或流量值
export function extractData(modelResult) {
    let minWlevel = Infinity; // 初始化最小值为负无穷大
    let maxWlevel = -Infinity; // 初始化最大值为正无穷大
    let minQ = Infinity;
    let maxQ = -Infinity;

    const times = []  // 存储时间的数组
    const firstData = modelResult[Object.keys(modelResult)[0]]; // 获取第一个对象的 data 属性
    for (const time in firstData.data) {
        times.push(time)
    }

    // 遍历每个 hedaoId 的数据 找到所有数据中水位最大小、流量最大v值
    for (const hedaoId in modelResult) {
        const { data } = modelResult[hedaoId];
        let minWlevel1 = Infinity; // 初始化最小值为负无穷大
        let maxWlevel1 = -Infinity; // 初始化最大值为正无穷大
        let minQ1 = Infinity;
        let maxQ1 = -Infinity;
        for (const time in data) {
            const { wlevel, q } = data[time];
            wlevel.forEach((item, index) => {
                wlevel[index] = parseFloat(wlevel[index]).toFixed(3)
            });
            let tempMaxWlevel = Math.max(...wlevel); // 计算当前时间的最大值
            let tempMaxQ = Math.max(...q); // 计算当前时间的最大值
            let tempMinWlevel = Math.min(...wlevel); // 计算当前时间的最大值
            let tempMinQ = Math.min(...q); // 计算当前时间的最大值
            if (tempMaxWlevel > maxWlevel1) {
                maxWlevel1 = Math.ceil(tempMaxWlevel); // 更新最大值
            }
            if (tempMaxQ > maxQ1) {
                maxQ1 = Math.ceil(tempMaxQ); // 更新最大值         
            }
            if (tempMinWlevel < minWlevel1) {
                minWlevel1 = Math.floor(tempMinWlevel); // 更新最大值
            }
            if (tempMinQ < minQ1) {
                minQ1 = Math.floor(tempMinQ); // 更新最大值
            }
        }
        modelResult[hedaoId]['maxWlevel'] = maxWlevel1; // 存储最大值
        modelResult[hedaoId]['minWlevel'] = minWlevel1; // 存储最大值
        modelResult[hedaoId]['maxQ'] = maxQ1; // 存储最大值
        modelResult[hedaoId]['minQ'] = minQ1; // 存储最大值
        if (maxWlevel1 > maxWlevel) {
            maxWlevel = maxWlevel1; // 更新最大值
        }
        if (maxQ1 > maxQ) {
            maxQ = maxQ1; // 更新最大值         
        }
        if (minWlevel1 < minWlevel) {
            minWlevel = minWlevel1; // 更新最大值
        }
        if (minQ1 < minQ) {
            minQ = minQ1; // 更新最大值
        }
    }
    return { times, maxWlevel, maxQ, minWlevel, minQ };
}

export function getChartsData(lineid, time, modelResult) {
    if (!(lineid in modelResult)) {
        return {
            wlevel: [],
            q: [],
            stakes: []
        }
    }
    const { data, stakes, maxWlevel, minWlevel, maxQ, minQ } = modelResult[lineid];
    const { wlevel, q } = data[time];
    return { wlevel, q, stakes, maxWlevel, minWlevel, maxQ, minQ }
}




