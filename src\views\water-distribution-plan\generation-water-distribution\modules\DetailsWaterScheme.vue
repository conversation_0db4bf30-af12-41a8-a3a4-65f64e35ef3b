<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="1200"
    @cancel="cancel"
    modalHeight="900"
    :footer="false"
  >
    <div slot="content">
      <div>
        <h3>基本信息</h3>
        <a-row>
          <a-col :lg="8" :md="8" :sm="8" :span="8">
            时间维度：{{ radioOptions.find(el => el.value == schemeDetails?.planType)?.label }}
          </a-col>
          <a-col :lg="8" :md="8" :sm="8" :span="8">方案名称：{{ schemeDetails?.msgName }}</a-col>
          <a-col :lg="8" :md="8" :sm="8" :span="8">
            配水时间段：{{ schemeDetails?.planStartDate }}~{{ schemeDetails?.planEndDate }}
          </a-col>
        </a-row>
        <a-row>
          <a-col :lg="8" :md="8" :sm="8" :span="8">创建时间：{{ schemeDetails?.createdTime }}</a-col>
          <a-col :lg="8" :md="8" :sm="8" :span="8">创建人：{{ schemeDetails?.createdUserName }}</a-col>
        </a-row>
      </div>
      <h3>配水方案生成</h3>
      <h4>配水参数获取方式：用水计划</h4>

      <vxe-table border :data="tableData" :merge-cells="mergeCells" :show-footer="true">
        <vxe-column field="irrigatedArea" title="所名" minWidth="90" align="center"></vxe-column>
        <vxe-column field="deptName" title="" minWidth="110" align="center">
          <template #default="{ row, rowIndex }">
            <div>{{ rowIndex == 3 ? '永济渠' : row.deptName }}</div>
          </template>
        </vxe-column>
        <vxe-column field="deptName" title="" minWidth="110" align="center"></vxe-column>

        <vxe-column v-for="el in dateList" :key="el" :title="el" minWidth="90" align="center">
          <template #default="{ row }">
            <div>{{ row.recordObj[el].planStartFlow }}&nbsp;~&nbsp;{{ row.recordObj[el].planEndFlow }}</div>
          </template>
        </vxe-column>
        <vxe-column title="备注" minWidth="110" align="center">
          <template #default="{ row, rowIndex }">
            <div>{{ rowIndex == 10 ? planRemarks : row.remarks }}</div>
          </template>
        </vxe-column>
      </vxe-table>

      <h4 class="mar10">配水参数获取方式：需水预测模型</h4>
      <vxe-table border :data="modelData" :merge-cells="mergeCells">
        <vxe-column field="irrigatedArea" title="所名" minWidth="90" align="center"></vxe-column>
        <vxe-column field="deptName" title="" minWidth="70" align="center">
          <template #default="{ row, rowIndex }">
            <div>{{ rowIndex == 3 ? '永济渠' : row.deptName }}</div>
          </template>
        </vxe-column>
        <vxe-column field="deptName" title="" minWidth="70" align="center"></vxe-column>

        <vxe-column v-for="el in modelDateList" :key="el" :title="el" minWidth="110" align="center">
          <template #default="{ row }">
            <div>{{ row.recordObj[el].planStartFlow }}&nbsp;~&nbsp;{{ row.recordObj[el].planEndFlow }}</div>
          </template>
        </vxe-column>
      </vxe-table>

      <h3 class="mar10" v-if="schemeVersionData?.length">方案调整记录</h3>
      <div style="padding-bottom: 20px" v-for="el in schemeVersionData" :key="el?.versionId">
        <div class="mar10" style="display: flex">
          <div>{{ el.versionCode }}</div>
          <div style="margin: 0 10px">修改人：{{ el.createdUserName }}</div>
          <div>修改时间：{{ el.createdTime }}</div>
        </div>

        <vxe-table border :data="el.versionList" :merge-cells="mergeCells" :show-footer="true">
          <vxe-column field="irrigatedArea" title="所名" minWidth="90" align="center"></vxe-column>
          <vxe-column field="deptName" title="" minWidth="110" align="center">
            <template #default="{ row, rowIndex }">
              <div>{{ rowIndex == 3 ? '永济渠' : row.deptName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="deptName" title="" minWidth="110" align="center"></vxe-column>

          <vxe-column v-for="el in versionDateList" :key="el" :title="el" minWidth="90" align="center">
            <template #default="{ row }">
              <div>{{ row.recordObj[el].planStartFlow }}&nbsp;~&nbsp;{{ row.recordObj[el].planEndFlow }}</div>
            </template>
          </vxe-column>
          <vxe-column field="remarks" title="备注" minWidth="110" align="center">
            <template #default="{ row, rowIndex }">
              <div>{{ rowIndex == 10 ? el?.remarks : row.remarks }}</div>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
    </div>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import { getSummariesById, getGenerationWaterDetails, getWaterSchemeVersion } from '../services'
  import { getOptions } from '@/api/common.js'
  import { getPlanReportDetails } from '../services.js'
  import { getFixedNum } from '@/utils/dealNumber.js'
  import * as _ from 'lodash'

  export default {
    name: 'DetailsWaterScheme',
    components: { AntModal },
    props: ['radioOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        schemeDetails: {},

        deptSortList: [],

        formTitle: '配水方案详情',
        dateList: [],
        modelDateList: [],
        versionDateList: [],

        schemeVersionData: [],
        tableData: [],
        modelData: [],
        mergeCells: [
          { row: 0, col: 0, rowspan: 11, colspan: 1 },
          { row: 0, col: 1, rowspan: 1, colspan: 2 },
          { row: 1, col: 1, rowspan: 1, colspan: 2 },
          { row: 2, col: 1, rowspan: 1, colspan: 2 },
          { row: 3, col: 1, rowspan: 6, colspan: 1 },
          { row: 9, col: 1, rowspan: 1, colspan: 2 },
          { row: 10, col: 1, rowspan: 1, colspan: 2 },
        ],
        planRemarks: '',
        // footerData: [{ irrigatedArea: '备注', deptName: '' }],
      }
    },

    watch: {},
    methods: {
      handleClose() {
        this.open = false
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },

      /** 详情按钮操作 */
      handleDetails(row) {
        this.schemeDetails = row
        this.open = true

        this.getDept()
        this.getApis(row)
      },
      getDept() {
        getOptions('planWaterDept').then(res => {
          this.deptSortList = res.data
        })
      },
      getApis(row) {
        Promise.allSettled([
          getPlanReportDetails({ planReportId: row.msgId }),
          //永济渠总局
          // getSummariesById({ msgId: row.msgId }),
        ]).then(results => {
          const planMsg = this.setSortDept(results[0]?.value?.data?.reportDetailsVOs) || []
          this.planRemarks = results[0]?.value?.data?.remarks || ''
          const summaryMsg = results[0]?.value?.data?.summaries || []
          const first = planMsg[0]?.records
          first?.forEach(item => {
            this.dateList.push(item.planDate)
          })
          this.tableData = planMsg.map(el => {
            const recordObj = {}
            el.records.forEach(item => {
              recordObj[item.planDate] = {
                planDate: item.planDate,
                planStartFlow: item.planStartFlow,
                planEndFlow: item.planEndFlow,
              }
            })
            return {
              ...el,
              irrigatedArea: '永济灌域',
              recordObj,
            }
          })

          //永济渠合计
          const yjTotalArr = this.tableData.slice(-6)
          const yjSum = {}
          //永济渠报总局
          const recordObj = {}
          summaryMsg.forEach(item => {
            recordObj[item.planDate] = {
              planDate: item.planDate,
              planStartFlow: item.planStartFlow,
              planEndFlow: item.planEndFlow,
            }
            yjSum[item.planDate] = {
              planDate: item.planDate,
              planStartFlow: this.sumNum1(yjTotalArr, item.planDate, 'planStartFlow'),
              planEndFlow: this.sumNum1(yjTotalArr, item.planDate, 'planEndFlow'),
            }
          })

          this.tableData.push(
            {
              deptId: 100001,
              deptName: '永济渠合计',
              irrigatedArea: '永济灌域',
              recordObj: yjSum,
              records: [],
              remarks: '',
            },
            {
              deptId: 100002,
              deptName: '永济渠报总局',
              irrigatedArea: '永济灌域',
              recordObj: recordObj,
              records: summaryMsg,
              remarks: results[0]?.value?.data?.remarks || '',
            },
          )
        })

        getGenerationWaterDetails({ waterSchemeId: row.waterSchemeId }).then(res => {
          if (res?.data?.length) {
            const firstRecord = res?.data[0]?.records
            firstRecord.forEach(item => {
              this.modelDateList.push(item.planDate)
            })
            console.log(this.modelDateList)
            let modelArr = this.setSortDept(res.data) || []
            this.modelData = modelArr.map(item => {
              const recordObj = {}
              const yjSum = {}
              item.records.forEach(item => {
                recordObj[item.planDate] = {
                  planDate: item.planDate,
                  planStartFlow: item.planStartFlow,
                  planEndFlow: item.planEndFlow,
                }
              })
              return {
                ...item,
                irrigatedArea: '永济灌域',
                recordObj,
              }
            })

            //永济渠合计
            const yjTotalArr = this.modelData.slice(-6)
            const yjSum = {}
            //永济渠报总局
            const recordObj = {}

            for (let i = 0; i < this.modelDateList.length; i++) {
              yjSum[this.modelDateList[i]] = {
                planDate: this.modelDateList[i],
                planStartFlow: this.sumNum1(yjTotalArr, this.modelDateList[i], 'planStartFlow'),
                planEndFlow: this.sumNum1(yjTotalArr, this.modelDateList[i], 'planEndFlow'),
              }
            }
            this.modelData.push({
              deptId: 100001,
              deptName: '永济渠合计',
              irrigatedArea: '永济渠合计',
              recordObj: yjSum,
              records: [],
              remarks: '',
            })
          }
        })
        getWaterSchemeVersion({ waterSchemeId: row.waterSchemeId }).then(res => {
          if (res?.data?.length) {
            const arr = this.setVersionSortDept(res.data) || []

            const first = arr[0]?.records[0]?.records
            first?.forEach(el => {
              this.versionDateList.push(el.planDate)
            })

            arr.forEach(el => {
              el.versionList = el.records.map(item => {
                const recordObj = {}
                item.records.forEach(item => {
                  recordObj[item.planDate] = {
                    planDate: item.planDate,
                    planStartFlow: item.planStartFlow,
                    planEndFlow: item.planEndFlow,
                  }
                })
                return {
                  ...item,
                  irrigatedArea: '永济灌域',
                  recordObj,
                }
              })
              //永济渠合计
              const yjTotalArr = el.versionList.slice(-6)
              const yjSum = {}
              //永济渠报总局
              const recordObj = {}
              for (let i = 0; i < this.versionDateList.length; i++) {
                recordObj[this.versionDateList[i]] = {
                  planDate: this.versionDateList[i],
                  planStartFlow: el.summaries[i].planStartFlow, //this.sumNum1(el.versionList, this.versionDateList[i], 'planStartFlow'),
                  planEndFlow: el.summaries[i].planEndFlow, //this.sumNum1(el.versionList, this.versionDateList[i], 'planEndFlow'),
                }
                yjSum[this.versionDateList[i]] = {
                  planDate: this.versionDateList[i],
                  planStartFlow: this.sumNum1(yjTotalArr, this.versionDateList[i], 'planStartFlow'),
                  planEndFlow: this.sumNum1(yjTotalArr, this.versionDateList[i], 'planEndFlow'),
                }
              }

              el.versionList.push(
                {
                  deptId: 100001,
                  deptName: '永济渠合计',
                  irrigatedArea: '永济灌域',
                  recordObj: yjSum,
                  records: [],
                  remarks: '',
                },
                {
                  deptId: 100002,
                  deptName: '永济渠报总局',
                  irrigatedArea: '永济灌域',
                  recordObj: recordObj,
                  records: [],
                  remarks: '',
                },
              )
            })
            this.schemeVersionData = arr
          }
        })
      },
      sumNum1(list, date, field) {
        let count = 0
        list.forEach((item, idx) => {
          count += Number(item?.recordObj[date][field])
        })
        return getFixedNum(count, 1)
      },
      //
      setSortDept(arr) {
        const keyToValue = {}
        this.deptSortList.forEach(item => {
          keyToValue[item.key] = item.value
        })
        if (!arr || arr.length === 0) {
          return []
        }
        const keyToIndex = {}
        this.deptSortList.forEach((item, index) => {
          keyToIndex[item.key] = index
        })
        arr.forEach(item => {
          if (keyToValue[item.deptId]) {
            item.deptName = keyToValue[item.deptId]
          }
        })
        arr.sort((a, b) => {
          const indexA = keyToIndex[a.deptId] ?? Infinity
          const indexB = keyToIndex[b.deptId] ?? Infinity
          return indexA - indexB
        })
        return arr
      },
      setVersionSortDept(arr) {
        const keyToValue = {}
        this.deptSortList.forEach(item => {
          keyToValue[item.key] = item.value
        })
        const keyToIndex = {}
        this.deptSortList.forEach((item, index) => {
          keyToIndex[item.key] = index
        })

        arr.forEach(item => {
          if (item.records) {
            item.records = item.records.filter(dept => keyToValue.hasOwnProperty(dept.deptId))

            item.records.forEach(dept => {
              dept.deptName = keyToValue[dept.deptId]
            })
            item.records.sort((a, b) => {
              return keyToIndex[a.deptId] - keyToIndex[b.deptId]
            })
          }
        })
        return arr
      },
    },
  }
</script>
<style lang="less" scoped>
  .mar10 {
    margin: 10px 0;
  }
  :deep(.vxe-table--render-default .vxe-cell) {
    padding: 0 6px !important;
    min-height: 30px !important;
  }
  :deep(.vxe-table--render-default .vxe-table--header) {
    table-layout: inherit;
  }
</style>
