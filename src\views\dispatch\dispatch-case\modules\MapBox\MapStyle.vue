<template>
  <div class="map-style">
    <div class="style-item item-common" v-for="(el, idx) in mapStyles.filter(el => el.label !== activeMapStyle)" :style="{
      backgroundImage: `url(${el.bg})`,
      display: 'none',
    }" @click="onStyleItemClick(el.label)">
      <div class="label">{{ el.label }}</div>
    </div>

    <div class="item-common b-(2px solid #ffffff)"
      :style="{ backgroundImage: `url(${mapStyles.find(el => el.label === activeMapStyle)?.bg})` }">
      <div class="label">{{ activeMapStyle }}</div>
    </div>
  </div>
</template>
<script lang="jsx">
import { clearSourceAndLayer } from '@/utils/mapUtils.js'
import {TianDiTu_Items} from './constant.js'
import getTianDiTuMapByConfig from './constant.js'
export default {
  name: 'MapStyle',
  props: ['mapIns', 'activeStyle'],
  data() {
    return {
      activeMapStyle: this.activeStyle || '卫星图',
      mapStyles: TianDiTu_Items,
    }
  },
  mounted() {
    this.addTianDiTuLayer(this.activeMapStyle)
  },
  methods: {
    onStyleItemClick(el, idx) {
      this.activeMapStyle = el.label
      this.addTianDiTuLayer(el.label)
    },

    addTianDiTuLayer(type) {
      clearSourceAndLayer(this.mapIns, ['mapbox-wmts-base-layer','mapbox-wmts-base-layer1'], ['mapbox-wmts-base-layer','mapbox-wmts-base-layer1'])
      getTianDiTuMapByConfig(type).forEach(element => {
        this.mapIns.addLayer({...element})
      });
    }
  },
}
</script>

<style lang="less" scoped>
.map-style {
  position: absolute;
  left: 20px;
  bottom: 20px;
  z-index: 999;
  border-radius: 4px;
  display: flex;
  padding: 2px;
  color: #ffffff;
  background-color: rgba(0, 0, 0, 0.5);

  &:hover {
    &::before {
      position: absolute;
      content: '';
      border-radius: 4px;
      width: calc(100% + 20px);
      height: calc(100% + 20px);
      background: rgba(255, 255, 255, 0.8);
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }

    .style-item {
      display: block !important;
    }
  }

  .item-common {
    width: 108px;
    height: 66px;
    position: relative;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-radius: 4px;
    user-select: none;
    cursor: pointer;

    .label {
      position: absolute;
      right: 0;
      bottom: 0;
      font-size: 12px;
      line-height: 12px;
      padding: 4px;
      background-color: #1890ff;
      border-radius: 2px;
    }
  }

  .style-item {
    margin-right: 10px;
    border: 2px solid #ffffff;
  }
}
</style>
